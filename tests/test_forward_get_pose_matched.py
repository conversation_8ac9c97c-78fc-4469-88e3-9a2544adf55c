#!/usr/bin/env python3
"""
测试forward_get_pose_matched方法的脚本

这个脚本用于验证修改后的可视化函数是否能正确处理forward_get_pose_matched的输出
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datasets.sceneleapplus_dataset import SceneLeapPlusDataset
from models.diffuser_lightning import DDPMLightning
from visualize_prediction_vs_ground_truth import (
    load_pretrained_model, predict_grasps_with_details, print_forward_get_pose_matched_details
)

def test_forward_get_pose_matched():
    """测试forward_get_pose_matched方法"""
    print("=" * 80)
    print("测试 forward_get_pose_matched 方法")
    print("=" * 80)
    
    # 配置参数
    root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3"
    succ_grasp_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
    obj_root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
    checkpoint_path = "path/to/your/checkpoint.ckpt"  # 需要用户指定
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    try:
        # 检查checkpoint路径
        if not os.path.exists(checkpoint_path):
            print(f"错误: checkpoint文件不存在: {checkpoint_path}")
            print("请修改脚本中的checkpoint_path变量为实际的模型文件路径")
            return
        
        # 初始化数据集
        print("正在初始化数据集...")
        dataset = SceneLeapPlusDataset(
            root_dir=root_dir,
            succ_grasp_dir=succ_grasp_dir,
            obj_root_dir=obj_root_dir,
            num_grasps=8,
            mode="camera_centric_scene_mean_normalized",
            max_grasps_per_object=2,
            mesh_scale=0.1,
            num_neg_prompts=4,
            enable_cropping=True,
            max_points=20000,
            grasp_sampling_strategy="random"
        )
        
        print(f"✓ 数据集初始化成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) == 0:
            print("数据集为空，请检查数据路径")
            return
        
        # 加载预训练模型
        print("正在加载预训练模型...")
        model = load_pretrained_model(checkpoint_path)
        model = model.to(device)
        
        # 获取一个样本进行测试
        sample_idx = 0
        sample = dataset[sample_idx]
        
        print(f"\n测试样本信息:")
        print(f"  - 场景ID: {sample['scene_id']}")
        print(f"  - 物体代码: {sample['obj_code']}")
        print(f"  - 点云形状: {sample['scene_pc'].shape}")
        print(f"  - 真实抓取形状: {sample['hand_model_pose'].shape}")
        
        # 准备批次数据
        batch = {}
        for key, value in sample.items():
            if isinstance(value, torch.Tensor):
                batch[key] = value.unsqueeze(0)
            else:
                batch[key] = [value]
        
        # 测试forward_get_pose_matched
        print("\n正在测试forward_get_pose_matched...")
        num_grasps = sample['hand_model_pose'].shape[0]
        batch_size = 1
        
        prediction_result = predict_grasps_with_details(model, batch, device, num_grasps=num_grasps)
        
        pred_poses = prediction_result['pred_poses']
        outputs = prediction_result['outputs']
        targets = prediction_result['targets']
        
        print(f"✓ 预测完成")
        print(f"  - 预测姿态形状: {pred_poses.shape}")
        
        # 打印详细信息
        print_forward_get_pose_matched_details(outputs, targets, batch_size, num_grasps)
        
        # 测试mesh创建
        print("\n正在测试mesh创建...")
        from visualize_prediction_vs_ground_truth import create_hand_meshes_from_outputs
        
        pred_meshes, gt_meshes = create_hand_meshes_from_outputs(
            outputs, targets, batch_size, num_grasps, max_grasps=3
        )
        
        print(f"✓ Mesh创建完成")
        print(f"  - 预测mesh数量: {len(pred_meshes)}")
        print(f"  - 真实mesh数量: {len(gt_meshes)}")
        
        print("\n" + "="*60)
        print("测试完成！")
        print("="*60)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_forward_get_pose_matched() 
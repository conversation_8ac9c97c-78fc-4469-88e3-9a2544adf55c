#!/usr/bin/env python3
"""
可视化功能测试脚本

测试内容：
1. 数据集加载
2. 模型配置验证
3. 误差计算功能
4. 手部mesh创建
5. 点云处理
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_dataset import SceneLeapPlusDataset
from utils.hand_model import HandModel, HandModelType
from visualize_prediction_vs_ground_truth import (
    calculate_pose_errors, create_hand_meshes_comparison,
    create_point_cloud_from_sample, create_highlighted_point_cloud
)

def test_dataset_loading():
    """测试数据集加载"""
    print("🔄 测试数据集加载...")
    
    # 使用真实路径
    root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3"
    succ_grasp_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
    obj_root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
    
    # 检查路径是否存在
    if not all(os.path.exists(p) for p in [root_dir, succ_grasp_dir, obj_root_dir]):
        print("⚠️ 数据路径不存在，跳过数据集测试")
        return False
    
    try:
        dataset = SceneLeapPlusDataset(
            root_dir=root_dir,
            succ_grasp_dir=succ_grasp_dir,
            obj_root_dir=obj_root_dir,
            num_grasps=4,
            mode="camera_centric_scene_mean_normalized",
            max_grasps_per_object=2,  # 使用小值加快测试
            mesh_scale=0.1,
            num_neg_prompts=2,
            enable_cropping=True,
            max_points=10000,
            grasp_sampling_strategy="random"
        )
        
        print(f"✅ 数据集加载成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) > 0:
            # 测试获取样本
            sample = dataset[0]
            print(f"✅ 样本获取成功")
            print(f"   - 场景ID: {sample['scene_id']}")
            print(f"   - 物体代码: {sample['obj_code']}")
            print(f"   - 点云形状: {sample['scene_pc'].shape}")
            print(f"   - 抓取形状: {sample['hand_model_pose'].shape}")
            return True
        else:
            print("⚠️ 数据集为空")
            return False
            
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        return False

def test_error_calculation():
    """测试误差计算功能"""
    print("\n🔄 测试误差计算功能...")
    
    try:
        # 创建模拟数据
        B, num_grasps, pose_dim = 2, 3, 23
        
        # 预测姿态
        pred_poses = torch.randn(B, num_grasps, pose_dim)
        
        # 真实姿态（添加一些噪声）
        gt_poses = pred_poses + 0.1 * torch.randn_like(pred_poses)
        
        # 计算误差
        errors = calculate_pose_errors(pred_poses, gt_poses, rot_type='r6d')
        
        # 验证结果
        required_keys = [
            'translation_mean', 'translation_std', 'translation_max', 'translation_min',
            'qpos_mean', 'qpos_std', 'qpos_max', 'qpos_min',
            'rotation_mean', 'rotation_std', 'rotation_max', 'rotation_min'
        ]
        
        for key in required_keys:
            if key not in errors:
                print(f"❌ 缺少误差指标: {key}")
                return False
            if not isinstance(errors[key], (int, float)):
                print(f"❌ 误差指标类型错误: {key}")
                return False
        
        print("✅ 误差计算功能正常")
        print(f"   - 位置误差: {errors['translation_mean']:.4f}")
        print(f"   - 关节误差: {errors['qpos_mean']:.4f}")
        print(f"   - 旋转误差: {errors['rotation_mean']:.4f}")
        return True
        
    except Exception as e:
        print(f"❌ 误差计算失败: {e}")
        return False

def test_hand_model():
    """测试手部模型功能"""
    print("\n🔄 测试手部模型功能...")
    
    try:
        # 初始化手部模型
        hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu')
        print("✅ 手部模型初始化成功")
        
        # 创建模拟姿态数据
        B, num_grasps, pose_dim = 1, 2, 23
        pred_poses = torch.randn(B, num_grasps, pose_dim)
        gt_poses = torch.randn(B, num_grasps, pose_dim)
        
        # 测试mesh创建
        pred_meshes, gt_meshes = create_hand_meshes_comparison(
            pred_poses, gt_poses, hand_model, max_grasps=2
        )
        
        print(f"✅ 手部mesh创建成功")
        print(f"   - 预测mesh数量: {len(pred_meshes)}")
        print(f"   - 真实mesh数量: {len(gt_meshes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手部模型测试失败: {e}")
        return False

def test_point_cloud_processing():
    """测试点云处理功能"""
    print("\n🔄 测试点云处理功能...")
    
    try:
        # 创建模拟点云数据
        N = 1000
        scene_pc = torch.randn(N, 6)  # xyz + rgb
        scene_pc[:, 3:6] = torch.rand(N, 3)  # 确保RGB在[0,1]范围内
        
        # 创建模拟物体掩码
        object_mask = torch.zeros(N, dtype=torch.bool)
        object_mask[:N//3] = True  # 前1/3的点是目标物体
        
        # 测试普通点云创建
        pcd = create_point_cloud_from_sample(scene_pc)
        print("✅ 普通点云创建成功")
        print(f"   - 点数量: {len(pcd.points)}")
        
        # 测试高亮点云创建
        background_pcd, object_pcd = create_highlighted_point_cloud(scene_pc, object_mask)
        print("✅ 高亮点云创建成功")
        print(f"   - 背景点数量: {len(background_pcd.points) if background_pcd else 0}")
        print(f"   - 目标点数量: {len(object_pcd.points) if object_pcd else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ 点云处理测试失败: {e}")
        return False

def test_config_validation():
    """测试配置文件验证"""
    print("\n🔄 测试配置文件验证...")
    
    try:
        from visualize_with_config import validate_config
        
        # 测试有效配置
        valid_config = {
            'dataset': {
                'root_dir': '/tmp',
                'succ_grasp_dir': '/tmp',
                'obj_root_dir': '/tmp'
            },
            'model': {
                'checkpoint_path': __file__  # 使用当前文件作为存在的路径
            }
        }
        
        # 注意：这个测试会因为路径不存在而产生警告，但不会失败
        result = validate_config(valid_config)
        print(f"✅ 配置验证功能正常 (结果: {result})")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False

def test_model_loading_simulation():
    """模拟测试模型加载（不实际加载模型）"""
    print("\n🔄 模拟测试模型加载...")
    
    try:
        # 检查模型类是否可以导入
        from models.diffuser_lightning import DDPMLightning
        print("✅ 模型类导入成功")
        
        # 检查必要的依赖
        import pytorch_lightning as pl
        from omegaconf import OmegaConf
        print("✅ 必要依赖检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型相关测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 80)
    print("可视化功能测试套件")
    print("=" * 80)
    
    tests = [
        ("数据集加载", test_dataset_loading),
        ("误差计算", test_error_calculation),
        ("手部模型", test_hand_model),
        ("点云处理", test_point_cloud_processing),
        ("配置验证", test_config_validation),
        ("模型加载模拟", test_model_loading_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results.append((test_name, False))
    
    # 打印测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可视化功能准备就绪。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    # 设置环境
    print("正在设置测试环境...")
    
    # 运行测试
    success = run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
超参数优化测试
系统性地找出最优的超参数配置

优化策略：
1. 网格搜索关键超参数
2. 贝叶斯优化
3. 学习率调度策略测试
4. 批次大小影响分析
5. 扩散步数优化
"""

import os
import sys
import torch
import numpy as np
import logging
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple
import itertools
from dataclasses import dataclass
import hydra
from omegaconf import DictConfig, OmegaConf
import matplotlib.pyplot as plt
import seaborn as sns
from torch.utils.data import DataLoader

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from models.diffuser_lightning import DDPMLightning
from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class HyperparameterConfig:
    """超参数配置"""
    learning_rate: float
    batch_size: int
    diffusion_steps: int
    beta_start: float
    beta_end: float
    guidance_scale: float
    loss_weights: Dict[str, float]
    optimizer_type: str = "adam"
    scheduler_type: str = "cosine"

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, base_config_path: str = "config/config.yaml"):
        """初始化优化器"""
        self.base_config_path = base_config_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.base_cfg = None
        self.dataset = None
        self.dataloader = None
        
        # 优化结果存储
        self.optimization_results = []
        
    def load_base_config(self):
        """加载基础配置"""
        logger.info("📋 加载基础配置...")
        self.base_cfg = OmegaConf.load(self.base_config_path)
        
    def create_test_dataset(self):
        """创建测试数据集"""
        logger.info("📊 创建测试数据集...")
        
        self.dataset = SceneLeapPlusDatasetCached(
            root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
            succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
            obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
            num_grasps=4,
            mode="camera_centric_scene_mean_normalized",
            max_grasps_per_object=2,
            mesh_scale=0.1,
            num_neg_prompts=4,
            enable_cropping=True,
            max_points=5000,
            grasp_sampling_strategy="random",
            cache_version="v2.0_hyperopt_test",
            use_exhaustive_sampling=False
        )
        
        # 限制数据集大小以加快测试
        if len(self.dataset) > 20:
            indices = list(range(20))
            self.dataset = torch.utils.data.Subset(self.dataset, indices)
        
        logger.info(f"✅ 测试数据集创建成功，样本数: {len(self.dataset)}")
    
    def create_model_with_config(self, hyperparams: HyperparameterConfig) -> DDPMLightning:
        """根据超参数创建模型"""
        # 复制基础配置
        cfg = OmegaConf.create(OmegaConf.to_container(self.base_cfg, resolve=True))
        
        # 更新超参数
        cfg.model.optimizer.lr = hyperparams.learning_rate
        cfg.model.steps = hyperparams.diffusion_steps
        cfg.model.schedule_cfg.beta[0] = hyperparams.beta_start
        cfg.model.schedule_cfg.beta[1] = hyperparams.beta_end
        cfg.model.guidance_scale = hyperparams.guidance_scale
        cfg.model.criterion.loss_weights.update(hyperparams.loss_weights)
        cfg.batch_size = hyperparams.batch_size
        
        # 创建模型
        model = DDPMLightning(cfg.model)
        model.to(self.device)
        
        return model
    
    def evaluate_hyperparameters(self, hyperparams: HyperparameterConfig, 
                                num_steps: int = 30) -> Dict[str, float]:
        """评估超参数配置"""
        logger.info(f"🔍 评估超参数配置: LR={hyperparams.learning_rate}, "
                   f"BS={hyperparams.batch_size}, Steps={hyperparams.diffusion_steps}")
        
        try:
            # 创建模型
            model = self.create_model_with_config(hyperparams)
            
            # 创建数据加载器
            dataloader = DataLoader(
                self.dataset, 
                batch_size=hyperparams.batch_size, 
                shuffle=True, 
                num_workers=0,
                collate_fn=SceneLeapPlusDatasetCached.collate_fn
            )
            
            # 设置优化器
            if hyperparams.optimizer_type == "adam":
                optimizer = torch.optim.Adam(model.parameters(), lr=hyperparams.learning_rate)
            elif hyperparams.optimizer_type == "adamw":
                optimizer = torch.optim.AdamW(model.parameters(), lr=hyperparams.learning_rate)
            else:
                optimizer = torch.optim.SGD(model.parameters(), lr=hyperparams.learning_rate)
            
            # 训练模式
            model.train()
            
            losses = []
            convergence_speed = 0
            stability_score = 0
            
            for step in range(num_steps):
                try:
                    batch = next(iter(dataloader))
                except StopIteration:
                    dataloader = DataLoader(
                        self.dataset, 
                        batch_size=hyperparams.batch_size, 
                        shuffle=True, 
                        num_workers=0,
                        collate_fn=SceneLeapPlusDatasetCached.collate_fn
                    )
                    batch = next(iter(dataloader))
                
                optimizer.zero_grad()
                
                # 前向传播
                loss_dict = model._compute_loss(batch, mode='train')
                total_loss = sum(v * hyperparams.loss_weights.get(k, 1.0) 
                               for k, v in loss_dict.items() if torch.is_tensor(v))
                
                # 检查损失是否有效
                if torch.isnan(total_loss) or torch.isinf(total_loss):
                    logger.warning(f"检测到无效损失: {total_loss}")
                    return {
                        'final_loss': float('inf'),
                        'convergence_speed': 0,
                        'stability_score': 0,
                        'valid': False
                    }
                
                # 反向传播
                total_loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                losses.append(float(total_loss))
            
            # 计算评估指标
            if len(losses) > 5:
                # 收敛速度：前5步和后5步的损失差异
                early_loss = np.mean(losses[:5])
                late_loss = np.mean(losses[-5:])
                convergence_speed = max(0, (early_loss - late_loss) / early_loss)
                
                # 稳定性：损失的变异系数（越小越稳定）
                loss_std = np.std(losses[-10:])
                loss_mean = np.mean(losses[-10:])
                stability_score = 1.0 / (1.0 + loss_std / max(loss_mean, 1e-8))
            
            final_loss = losses[-1] if losses else float('inf')
            
            # 清理内存
            del model
            torch.cuda.empty_cache()
            
            return {
                'final_loss': final_loss,
                'convergence_speed': convergence_speed,
                'stability_score': stability_score,
                'loss_history': losses,
                'valid': True
            }
            
        except Exception as e:
            logger.error(f"❌ 超参数评估失败: {e}")
            return {
                'final_loss': float('inf'),
                'convergence_speed': 0,
                'stability_score': 0,
                'valid': False,
                'error': str(e)
            }
    
    def grid_search_optimization(self) -> List[Dict[str, Any]]:
        """网格搜索优化"""
        logger.info("🔍 开始网格搜索优化...")
        
        # 定义搜索空间
        search_space = {
            'learning_rate': [1e-4, 5e-4, 1e-3],
            'batch_size': [4, 8],  # 受限于测试数据集大小
            'diffusion_steps': [50, 100],
            'beta_start': [0.0001, 0.001],
            'beta_end': [0.01, 0.02],
            'guidance_scale': [5.0, 7.5, 10.0]
        }
        
        # 固定损失权重
        base_loss_weights = {
            'translation': 10.0,
            'rotation': 20.0,
            'qpos': 1.0,
            'neg_loss': 0.5
        }
        
        # 生成所有组合
        keys = list(search_space.keys())
        values = list(search_space.values())
        combinations = list(itertools.product(*values))
        
        logger.info(f"总共需要测试 {len(combinations)} 个配置")
        
        results = []
        
        for i, combination in enumerate(combinations):
            # 创建超参数配置
            hyperparams = HyperparameterConfig(
                learning_rate=combination[0],
                batch_size=combination[1],
                diffusion_steps=combination[2],
                beta_start=combination[3],
                beta_end=combination[4],
                guidance_scale=combination[5],
                loss_weights=base_loss_weights
            )
            
            logger.info(f"测试配置 {i+1}/{len(combinations)}")
            
            # 评估配置
            metrics = self.evaluate_hyperparameters(hyperparams, num_steps=20)
            
            # 存储结果
            result = {
                'config_id': i,
                'hyperparams': {
                    'learning_rate': hyperparams.learning_rate,
                    'batch_size': hyperparams.batch_size,
                    'diffusion_steps': hyperparams.diffusion_steps,
                    'beta_start': hyperparams.beta_start,
                    'beta_end': hyperparams.beta_end,
                    'guidance_scale': hyperparams.guidance_scale,
                },
                'metrics': metrics
            }
            
            results.append(result)
            
            # 计算综合得分
            if metrics['valid']:
                # 综合得分：考虑最终损失、收敛速度和稳定性
                score = (
                    1.0 / (1.0 + metrics['final_loss']) * 0.5 +
                    metrics['convergence_speed'] * 0.3 +
                    metrics['stability_score'] * 0.2
                )
                result['score'] = score
                
                logger.info(f"配置 {i+1} - 得分: {score:.4f}, "
                           f"最终损失: {metrics['final_loss']:.4f}, "
                           f"收敛速度: {metrics['convergence_speed']:.4f}")
            else:
                result['score'] = 0.0
                logger.warning(f"配置 {i+1} - 无效")
        
        return results
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析优化结果"""
        logger.info("📊 分析优化结果...")
        
        # 过滤有效结果
        valid_results = [r for r in results if r['metrics']['valid']]
        
        if not valid_results:
            logger.error("❌ 没有有效的结果")
            return {}
        
        # 按得分排序
        valid_results.sort(key=lambda x: x['score'], reverse=True)
        
        # 最佳配置
        best_config = valid_results[0]
        
        # 参数重要性分析
        param_importance = {}
        for param in ['learning_rate', 'batch_size', 'diffusion_steps', 'beta_start', 'beta_end', 'guidance_scale']:
            param_values = {}
            for result in valid_results:
                value = result['hyperparams'][param]
                if value not in param_values:
                    param_values[value] = []
                param_values[value].append(result['score'])
            
            # 计算每个值的平均得分
            avg_scores = {v: np.mean(scores) for v, scores in param_values.items()}
            param_importance[param] = {
                'best_value': max(avg_scores.keys(), key=lambda x: avg_scores[x]),
                'score_range': max(avg_scores.values()) - min(avg_scores.values()),
                'avg_scores': avg_scores
            }
        
        analysis = {
            'best_config': best_config,
            'top_5_configs': valid_results[:5],
            'param_importance': param_importance,
            'total_tested': len(results),
            'valid_configs': len(valid_results),
            'success_rate': len(valid_results) / len(results)
        }
        
        # 打印分析结果
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 超参数优化分析结果")
        logger.info(f"{'='*60}")
        logger.info(f"总测试配置数: {analysis['total_tested']}")
        logger.info(f"有效配置数: {analysis['valid_configs']}")
        logger.info(f"成功率: {analysis['success_rate']:.1%}")
        
        logger.info(f"\n🏆 最佳配置 (得分: {best_config['score']:.4f}):")
        for param, value in best_config['hyperparams'].items():
            logger.info(f"  {param}: {value}")
        
        logger.info(f"\n📈 参数重要性排序:")
        sorted_params = sorted(param_importance.items(), 
                             key=lambda x: x[1]['score_range'], reverse=True)
        for param, info in sorted_params:
            logger.info(f"  {param}: 最佳值={info['best_value']}, "
                       f"得分范围={info['score_range']:.4f}")
        
        return analysis
    
    def run_optimization(self) -> Dict[str, Any]:
        """运行完整的超参数优化"""
        logger.info("🚀 开始超参数优化...")
        
        start_time = time.time()
        
        # 初始化
        self.load_base_config()
        self.create_test_dataset()
        
        # 网格搜索
        results = self.grid_search_optimization()
        
        # 分析结果
        analysis = self.analyze_results(results)
        
        end_time = time.time()
        
        # 添加执行信息
        analysis['execution_time'] = end_time - start_time
        analysis['all_results'] = results
        
        logger.info(f"✅ 超参数优化完成，耗时: {analysis['execution_time']:.2f}秒")
        
        return analysis

def main():
    """主函数"""
    # 配置环境
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    # 创建优化器
    optimizer = HyperparameterOptimizer()
    
    # 运行优化
    results = optimizer.run_optimization()
    
    # 保存结果
    results_path = "tests/hyperparameter_optimization_results.json"
    
    # 转换不可序列化的对象
    serializable_results = {}
    for k, v in results.items():
        if isinstance(v, (list, dict, str, int, float, bool)) or v is None:
            serializable_results[k] = v
        else:
            serializable_results[k] = str(v)
    
    with open(results_path, 'w') as f:
        json.dump(serializable_results, f, indent=2)
    
    logger.info(f"📄 优化结果已保存到: {results_path}")
    
    return results

if __name__ == "__main__":
    main()

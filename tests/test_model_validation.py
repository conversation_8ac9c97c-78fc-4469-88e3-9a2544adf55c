#!/usr/bin/env python3
"""
扩散模型架构验证测试
验证模型的基本学习能力和架构完整性
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple
import hydra
from omegaconf import DictConfig, OmegaConf
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from models.diffuser_lightning import DDPMLightning
from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
from torch.utils.data import DataLoader
from utils.hand_helper import process_hand_pose

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelValidationTester:
    """模型验证测试器"""
    
    def __init__(self, config_path: str = "config/train_diffuser.yaml"):
        """初始化测试器"""
        self.config_path = config_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.results = {}
        
    def load_config_and_model(self) -> Tuple[DictConfig, DDPMLightning]:
        """加载配置和模型"""
        logger.info("加载配置和模型...")
        
        # 加载配置
        with hydra.initialize(config_path="../config", version_base=None):
            cfg = hydra.compose(config_name="train_diffuser.yaml")
        
        # 创建模型
        model = DDPMLightning(cfg.model)
        model.to(self.device)
        model.eval()
        
        logger.info(f"模型加载完成，设备: {self.device}")
        logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        return cfg, model
    
    def create_test_dataset(self, cfg: DictConfig, max_samples: int = 10) -> DataLoader:
        """创建小规模测试数据集"""
        logger.info(f"创建测试数据集，最大样本数: {max_samples}")
        
        # 使用真实路径但限制数据量
        dataset = SceneLeapPlusDatasetCached(
            root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
            succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
            obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
            num_grasps=2,  # 减少抓取数量
            mode=cfg.mode,
            max_grasps_per_object=2,  # 限制每个对象的抓取数
            mesh_scale=0.1,
            num_neg_prompts=2,  # 减少负向提示数量
            enable_cropping=True,
            max_points=1000,  # 减少点云大小
            grasp_sampling_strategy="random",
            cache_version="v1.0_validation_test",
            cache_mode="train"
        )
        
        # 限制数据集大小
        if len(dataset) > max_samples:
            indices = torch.randperm(len(dataset))[:max_samples]
            dataset = torch.utils.data.Subset(dataset, indices)
        
        dataloader = DataLoader(
            dataset,
            batch_size=2,  # 小批次
            shuffle=False,
            num_workers=0,  # 避免多进程问题
            collate_fn=SceneLeapPlusDatasetCached.collate_fn
        )
        
        logger.info(f"数据集创建完成，样本数: {len(dataset)}")
        return dataloader
    
    def test_forward_pass(self, model: DDPMLightning, dataloader: DataLoader) -> Dict[str, Any]:
        """测试前向传播"""
        logger.info("测试前向传播...")
        
        results = {
            "success": False,
            "error": None,
            "output_shapes": {},
            "loss_values": {},
            "gradient_norms": {}
        }
        
        try:
            batch = next(iter(dataloader))
            
            # 移动数据到设备
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[key] = value.to(self.device)
            
            # 前向传播
            with torch.no_grad():
                # 测试推理模式
                model.eval()
                output = model.forward(batch)
                results["output_shapes"]["inference"] = {k: v.shape for k, v in output.items() if isinstance(v, torch.Tensor)}
                
                # 测试训练模式
                model.train()
                loss, loss_dict, processed_batch = model._compute_loss(batch, mode='train')
                results["loss_values"] = {k: float(v) for k, v in loss_dict.items()}
                results["loss_values"]["total_loss"] = float(loss)
                
                # 检查梯度
                loss.backward()
                grad_norms = {}
                for name, param in model.named_parameters():
                    if param.grad is not None:
                        grad_norms[name] = float(param.grad.norm())
                results["gradient_norms"] = grad_norms
                
                # 清除梯度
                model.zero_grad()
            
            results["success"] = True
            logger.info("前向传播测试成功")
            
        except Exception as e:
            results["error"] = str(e)
            logger.error(f"前向传播测试失败: {e}")
        
        return results
    
    def test_overfitting(self, model: DDPMLightning, dataloader: DataLoader, steps: int = 50) -> Dict[str, Any]:
        """过拟合测试 - 验证模型能否在小数据集上过拟合"""
        logger.info(f"开始过拟合测试，训练步数: {steps}")
        
        results = {
            "success": False,
            "error": None,
            "loss_history": [],
            "final_loss": None,
            "convergence_achieved": False
        }
        
        try:
            # 获取单个批次用于过拟合
            batch = next(iter(dataloader))
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[key] = value.to(self.device)
            
            # 设置优化器
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
            model.train()
            
            loss_history = []
            
            for step in range(steps):
                optimizer.zero_grad()
                
                # 前向传播和损失计算
                loss, loss_dict, _ = model._compute_loss(batch, mode='train')
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                loss_value = float(loss)
                loss_history.append(loss_value)
                
                if step % 10 == 0:
                    logger.info(f"Step {step}, Loss: {loss_value:.6f}")
            
            results["loss_history"] = loss_history
            results["final_loss"] = loss_history[-1]
            results["convergence_achieved"] = loss_history[-1] < loss_history[0] * 0.1  # 损失下降90%
            results["success"] = True
            
            logger.info(f"过拟合测试完成，最终损失: {results['final_loss']:.6f}")
            
        except Exception as e:
            results["error"] = str(e)
            logger.error(f"过拟合测试失败: {e}")
        
        return results
    
    def test_dimension_consistency(self, model: DDPMLightning, dataloader: DataLoader) -> Dict[str, Any]:
        """测试维度一致性"""
        logger.info("测试维度一致性...")
        
        results = {
            "success": False,
            "error": None,
            "dimension_checks": {}
        }
        
        try:
            batch = next(iter(dataloader))
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[key] = value.to(self.device)
            
            model.eval()
            
            # 检查各个组件的维度
            with torch.no_grad():
                # 1. 数据预处理
                processed_batch = process_hand_pose(batch, rot_type=model.rot_type, mode=model.mode)
                norm_pose = processed_batch['norm_pose']
                
                results["dimension_checks"]["norm_pose"] = list(norm_pose.shape)
                
                # 2. 条件编码
                condition_dict = model.eps_model.condition(processed_batch)
                for key, value in condition_dict.items():
                    if isinstance(value, torch.Tensor):
                        results["dimension_checks"][f"condition_{key}"] = list(value.shape)
                
                # 3. 扩散过程
                B = norm_pose.shape[0]
                ts = model._sample_timesteps(B)
                noise = torch.randn_like(norm_pose, device=model.device)
                x_t = model.q_sample(x0=norm_pose, t=ts, noise=noise)
                
                results["dimension_checks"]["timesteps"] = list(ts.shape)
                results["dimension_checks"]["noise"] = list(noise.shape)
                results["dimension_checks"]["x_t"] = list(x_t.shape)
                
                # 4. UNet输出
                processed_batch.update(condition_dict)
                output = model.eps_model(x_t, ts, processed_batch)
                results["dimension_checks"]["unet_output"] = list(output.shape)
            
            results["success"] = True
            logger.info("维度一致性测试成功")
            
        except Exception as e:
            results["error"] = str(e)
            logger.error(f"维度一致性测试失败: {e}")
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有验证测试"""
        logger.info("开始模型验证测试...")
        
        try:
            # 加载配置和模型
            cfg, model = self.load_config_and_model()
            
            # 创建测试数据集
            dataloader = self.create_test_dataset(cfg)
            
            # 运行各项测试
            self.results["forward_pass"] = self.test_forward_pass(model, dataloader)
            self.results["dimension_consistency"] = self.test_dimension_consistency(model, dataloader)
            self.results["overfitting"] = self.test_overfitting(model, dataloader)
            
            # 生成测试报告
            self.generate_report()
            
        except Exception as e:
            logger.error(f"测试运行失败: {e}")
            self.results["global_error"] = str(e)
        
        return self.results
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("生成测试报告...")
        
        print("\n" + "="*60)
        print("模型验证测试报告")
        print("="*60)
        
        # 前向传播测试
        if "forward_pass" in self.results:
            fp_result = self.results["forward_pass"]
            print(f"\n1. 前向传播测试: {'✅ 通过' if fp_result['success'] else '❌ 失败'}")
            if fp_result["success"]:
                print(f"   - 损失值: {fp_result['loss_values']}")
                print(f"   - 梯度范数数量: {len(fp_result['gradient_norms'])}")
            else:
                print(f"   - 错误: {fp_result['error']}")
        
        # 维度一致性测试
        if "dimension_consistency" in self.results:
            dc_result = self.results["dimension_consistency"]
            print(f"\n2. 维度一致性测试: {'✅ 通过' if dc_result['success'] else '❌ 失败'}")
            if dc_result["success"]:
                print("   - 关键维度:")
                for key, shape in dc_result["dimension_checks"].items():
                    print(f"     {key}: {shape}")
            else:
                print(f"   - 错误: {dc_result['error']}")
        
        # 过拟合测试
        if "overfitting" in self.results:
            of_result = self.results["overfitting"]
            print(f"\n3. 过拟合测试: {'✅ 通过' if of_result['success'] else '❌ 失败'}")
            if of_result["success"]:
                print(f"   - 初始损失: {of_result['loss_history'][0]:.6f}")
                print(f"   - 最终损失: {of_result['final_loss']:.6f}")
                print(f"   - 收敛达成: {'是' if of_result['convergence_achieved'] else '否'}")
            else:
                print(f"   - 错误: {of_result['error']}")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    # 配置环境
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    # 运行测试
    tester = ModelValidationTester()
    results = tester.run_all_tests()
    
    # 保存结果
    import json
    with open("tests/model_validation_results.json", "w") as f:
        # 转换tensor为可序列化格式
        serializable_results = {}
        for key, value in results.items():
            if isinstance(value, dict):
                serializable_results[key] = {k: v for k, v in value.items() if not isinstance(v, torch.Tensor)}
            else:
                serializable_results[key] = value
        json.dump(serializable_results, f, indent=2)
    
    logger.info("测试完成，结果已保存到 tests/model_validation_results.json")

if __name__ == "__main__":
    main()

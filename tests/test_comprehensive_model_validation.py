#!/usr/bin/env python3
"""
综合模型验证测试
验证扩散模型的基础功能和架构完整性

测试内容：
1. 数据流完整性测试
2. 梯度流检查
3. 过拟合能力测试
4. 维度一致性验证
5. 损失函数验证
6. 内存使用分析
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import logging
import time
import traceback
from pathlib import Path
from typing import Dict, Any, List, Tuple
import hydra
from omegaconf import DictConfig, OmegaConf
import matplotlib.pyplot as plt
import seaborn as sns
from torch.utils.data import DataLoader

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from models.diffuser_lightning import DDPMLightning
from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
from utils.hand_helper import process_hand_pose

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelValidator:
    """模型验证器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化验证器"""
        self.config_path = config_path
        self.model = None
        self.dataset = None
        self.dataloader = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 测试结果存储
        self.test_results = {}
        
    def load_config_and_model(self):
        """加载配置和模型"""
        logger.info("🔧 加载配置和模型...")
        
        try:
            # 加载配置
            cfg = OmegaConf.load(self.config_path)
            
            # 创建模型
            self.model = DDPMLightning(cfg.model)
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"✅ 模型加载成功，参数量: {sum(p.numel() for p in self.model.parameters()):,}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            traceback.print_exc()
            return False
    
    def create_test_dataset(self, max_samples: int = 10):
        """创建测试数据集"""
        logger.info("📊 创建测试数据集...")
        
        try:
            # 使用真实路径但限制样本数量
            self.dataset = SceneLeapPlusDatasetCached(
                root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
                succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
                obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
                num_grasps=4,
                mode="camera_centric_scene_mean_normalized",
                max_grasps_per_object=2,  # 限制抓取数量
                mesh_scale=0.1,
                num_neg_prompts=4,
                enable_cropping=True,
                max_points=5000,  # 减少点云大小
                grasp_sampling_strategy="random",
                cache_version="v2.0_validation_test",
                use_exhaustive_sampling=False
            )
            
            # 限制数据集大小
            if len(self.dataset) > max_samples:
                indices = list(range(min(max_samples, len(self.dataset))))
                self.dataset = torch.utils.data.Subset(self.dataset, indices)
            
            self.dataloader = DataLoader(
                self.dataset, 
                batch_size=2, 
                shuffle=False, 
                num_workers=0,  # 避免多进程问题
                collate_fn=SceneLeapPlusDatasetCached.collate_fn
            )
            
            logger.info(f"✅ 测试数据集创建成功，样本数: {len(self.dataset)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据集创建失败: {e}")
            traceback.print_exc()
            return False
    
    def test_data_flow(self) -> bool:
        """测试数据流完整性"""
        logger.info("🔄 测试数据流完整性...")
        
        try:
            # 获取一个批次
            batch = next(iter(self.dataloader))
            
            # 检查输入数据格式
            required_keys = ['scene_pc', 'hand_model_pose', 'se3', 'positive_prompt']
            for key in required_keys:
                if key not in batch:
                    raise ValueError(f"缺少必需的键: {key}")
            
            # 检查数据维度
            scene_pc = batch['scene_pc']
            hand_pose = batch['hand_model_pose']
            se3 = batch['se3']
            
            logger.info(f"场景点云形状: {scene_pc.shape}")
            logger.info(f"手部姿态形状: {hand_pose.shape}")
            logger.info(f"SE3变换形状: {se3.shape}")
            
            # 数据预处理
            processed_batch = process_hand_pose(batch, rot_type=self.model.rot_type, mode=self.model.mode)
            
            # 检查预处理后的数据
            if 'norm_pose' not in processed_batch:
                raise ValueError("预处理后缺少 norm_pose")
            
            norm_pose = processed_batch['norm_pose']
            logger.info(f"标准化姿态形状: {norm_pose.shape}")
            
            # 条件编码
            condition_dict = self.model.eps_model.condition(processed_batch)
            processed_batch.update(condition_dict)
            
            # 检查条件编码结果
            if 'scene_cond' not in processed_batch:
                raise ValueError("条件编码后缺少 scene_cond")
            
            scene_cond = processed_batch['scene_cond']
            logger.info(f"场景条件形状: {scene_cond.shape}")
            
            self.test_results['data_flow'] = {
                'status': 'PASS',
                'input_shapes': {
                    'scene_pc': list(scene_pc.shape),
                    'hand_pose': list(hand_pose.shape),
                    'se3': list(se3.shape)
                },
                'processed_shapes': {
                    'norm_pose': list(norm_pose.shape),
                    'scene_cond': list(scene_cond.shape)
                }
            }
            
            logger.info("✅ 数据流测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据流测试失败: {e}")
            self.test_results['data_flow'] = {'status': 'FAIL', 'error': str(e)}
            return False
    
    def test_forward_pass(self) -> bool:
        """测试前向传播"""
        logger.info("➡️ 测试前向传播...")
        
        try:
            batch = next(iter(self.dataloader))
            
            # 数据预处理
            processed_batch = process_hand_pose(batch, rot_type=self.model.rot_type, mode=self.model.mode)
            
            # 条件编码
            condition_dict = self.model.eps_model.condition(processed_batch)
            processed_batch.update(condition_dict)
            
            # 前向传播
            with torch.no_grad():
                # 训练模式的前向传播
                self.model.train()
                loss_dict = self.model._compute_loss(batch, mode='train')
                
                # 推理模式的前向传播
                self.model.eval()
                output = self.model.forward(processed_batch)
            
            # 检查输出
            if not isinstance(loss_dict, dict):
                raise ValueError("损失输出应该是字典")
            
            if not isinstance(output, dict):
                raise ValueError("模型输出应该是字典")
            
            if 'pred_pose_norm' not in output:
                raise ValueError("模型输出缺少 pred_pose_norm")
            
            pred_pose = output['pred_pose_norm']
            logger.info(f"预测姿态形状: {pred_pose.shape}")
            logger.info(f"损失项: {list(loss_dict.keys())}")
            
            self.test_results['forward_pass'] = {
                'status': 'PASS',
                'output_shape': list(pred_pose.shape),
                'loss_keys': list(loss_dict.keys()),
                'loss_values': {k: float(v) for k, v in loss_dict.items() if torch.is_tensor(v)}
            }
            
            logger.info("✅ 前向传播测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 前向传播测试失败: {e}")
            self.test_results['forward_pass'] = {'status': 'FAIL', 'error': str(e)}
            return False
    
    def test_gradient_flow(self) -> bool:
        """测试梯度流"""
        logger.info("🔄 测试梯度流...")
        
        try:
            batch = next(iter(self.dataloader))
            
            # 设置为训练模式
            self.model.train()
            
            # 清零梯度
            self.model.zero_grad()
            
            # 前向传播和损失计算
            loss_dict = self.model._compute_loss(batch, mode='train')
            total_loss = sum(v * self.model.loss_weights.get(k, 1.0) for k, v in loss_dict.items() if torch.is_tensor(v))
            
            # 反向传播
            total_loss.backward()
            
            # 检查梯度
            grad_norms = {}
            zero_grad_params = 0
            total_params = 0
            
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    grad_norm = param.grad.norm().item()
                    grad_norms[name] = grad_norm
                    if grad_norm == 0:
                        zero_grad_params += 1
                else:
                    zero_grad_params += 1
                total_params += 1
            
            # 计算统计信息
            if grad_norms:
                max_grad = max(grad_norms.values())
                min_grad = min(grad_norms.values())
                avg_grad = sum(grad_norms.values()) / len(grad_norms)
            else:
                max_grad = min_grad = avg_grad = 0
            
            logger.info(f"总参数数: {total_params}")
            logger.info(f"零梯度参数数: {zero_grad_params}")
            logger.info(f"梯度范围: [{min_grad:.6f}, {max_grad:.6f}]")
            logger.info(f"平均梯度: {avg_grad:.6f}")
            
            # 检查梯度异常
            gradient_health = "HEALTHY"
            if max_grad > 100:
                gradient_health = "EXPLODING"
            elif avg_grad < 1e-8:
                gradient_health = "VANISHING"
            elif zero_grad_params / total_params > 0.5:
                gradient_health = "SPARSE"
            
            self.test_results['gradient_flow'] = {
                'status': 'PASS',
                'total_params': total_params,
                'zero_grad_params': zero_grad_params,
                'gradient_stats': {
                    'max': max_grad,
                    'min': min_grad,
                    'avg': avg_grad
                },
                'gradient_health': gradient_health,
                'total_loss': float(total_loss)
            }
            
            logger.info(f"✅ 梯度流测试通过，梯度健康状态: {gradient_health}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 梯度流测试失败: {e}")
            self.test_results['gradient_flow'] = {'status': 'FAIL', 'error': str(e)}
            return False
    
    def test_overfitting_capability(self, num_steps: int = 50) -> bool:
        """测试过拟合能力"""
        logger.info("🎯 测试过拟合能力...")
        
        try:
            # 获取单个样本
            batch = next(iter(self.dataloader))
            
            # 设置优化器
            optimizer = torch.optim.Adam(self.model.parameters(), lr=1e-3)
            
            # 训练模式
            self.model.train()
            
            losses = []
            
            for step in range(num_steps):
                optimizer.zero_grad()
                
                # 前向传播
                loss_dict = self.model._compute_loss(batch, mode='train')
                total_loss = sum(v * self.model.loss_weights.get(k, 1.0) for k, v in loss_dict.items() if torch.is_tensor(v))
                
                # 反向传播
                total_loss.backward()
                optimizer.step()
                
                losses.append(float(total_loss))
                
                if step % 10 == 0:
                    logger.info(f"Step {step}: Loss = {total_loss:.6f}")
            
            # 分析损失趋势
            initial_loss = losses[0]
            final_loss = losses[-1]
            loss_reduction = (initial_loss - final_loss) / initial_loss
            
            # 判断是否能够过拟合
            can_overfit = loss_reduction > 0.1  # 损失下降超过10%
            
            self.test_results['overfitting'] = {
                'status': 'PASS' if can_overfit else 'FAIL',
                'initial_loss': initial_loss,
                'final_loss': final_loss,
                'loss_reduction': loss_reduction,
                'can_overfit': can_overfit,
                'loss_history': losses
            }
            
            if can_overfit:
                logger.info(f"✅ 过拟合测试通过，损失下降: {loss_reduction:.2%}")
            else:
                logger.warning(f"⚠️ 过拟合测试失败，损失下降: {loss_reduction:.2%}")
            
            return can_overfit
            
        except Exception as e:
            logger.error(f"❌ 过拟合测试失败: {e}")
            self.test_results['overfitting'] = {'status': 'FAIL', 'error': str(e)}
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始综合模型验证...")
        
        start_time = time.time()
        
        # 测试序列
        tests = [
            ("配置和模型加载", self.load_config_and_model),
            ("测试数据集创建", self.create_test_dataset),
            ("数据流完整性", self.test_data_flow),
            ("前向传播", self.test_forward_pass),
            ("梯度流", self.test_gradient_flow),
            ("过拟合能力", self.test_overfitting_capability),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"执行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        end_time = time.time()
        
        # 生成总结报告
        summary = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': passed_tests / total_tests,
            'execution_time': end_time - start_time,
            'test_results': self.test_results,
            'overall_status': 'PASS' if passed_tests == total_tests else 'PARTIAL' if passed_tests > 0 else 'FAIL'
        }
        
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 验证总结")
        logger.info(f"{'='*60}")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试数: {passed_tests}")
        logger.info(f"成功率: {summary['success_rate']:.1%}")
        logger.info(f"执行时间: {summary['execution_time']:.2f}秒")
        logger.info(f"整体状态: {summary['overall_status']}")
        
        return summary

def main():
    """主函数"""
    # 配置环境
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    # 创建验证器
    validator = ModelValidator()
    
    # 运行验证
    results = validator.run_all_tests()
    
    # 保存结果
    import json
    results_path = "tests/model_validation_results.json"
    with open(results_path, 'w') as f:
        # 转换不可序列化的对象
        serializable_results = {}
        for k, v in results.items():
            if k == 'test_results':
                serializable_results[k] = {}
                for test_name, test_result in v.items():
                    serializable_results[k][test_name] = {}
                    for key, value in test_result.items():
                        if isinstance(value, (list, dict, str, int, float, bool)) or value is None:
                            serializable_results[k][test_name][key] = value
                        else:
                            serializable_results[k][test_name][key] = str(value)
            else:
                serializable_results[k] = v
        
        json.dump(serializable_results, f, indent=2)
    
    logger.info(f"📄 验证结果已保存到: {results_path}")
    
    return results

if __name__ == "__main__":
    main()

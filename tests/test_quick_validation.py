#!/usr/bin/env python3
"""
快速模型验证脚本
用于初步检查模型的基本功能

验证内容：
1. 模型加载和初始化
2. 数据加载和预处理
3. 前向传播
4. 损失计算
5. 简单的训练步骤
"""

import os
import sys
import torch
import logging
from pathlib import Path
from omegaconf import OmegaConf

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from models.diffuser_lightning import DDPMLightning
from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
from torch.utils.data import DataLoader

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_validation():
    """快速验证函数"""
    logger.info("🚀 开始快速模型验证...")
    
    try:
        # 1. 加载配置
        logger.info("📋 加载配置...")
        cfg = OmegaConf.load("config/config.yaml")
        logger.info("✅ 配置加载成功")
        
        # 2. 创建模型
        logger.info("🤖 创建模型...")
        model = DDPMLightning(cfg.model)
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        logger.info(f"✅ 模型创建成功，设备: {device}")
        logger.info(f"📊 模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 3. 创建数据集
        logger.info("📊 创建数据集...")
        dataset = SceneLeapPlusDatasetCached(
            root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
            succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
            obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
            num_grasps=4,
            mode="camera_centric_scene_mean_normalized",
            max_grasps_per_object=2,
            mesh_scale=0.1,
            num_neg_prompts=4,
            enable_cropping=True,
            max_points=5000,
            grasp_sampling_strategy="random",
            cache_version="v2.0_quick_test",
            use_exhaustive_sampling=False
        )
        
        # 限制数据集大小
        if len(dataset) > 5:
            indices = list(range(5))
            dataset = torch.utils.data.Subset(dataset, indices)
        
        dataloader = DataLoader(
            dataset, 
            batch_size=2, 
            shuffle=False, 
            num_workers=0,
            collate_fn=SceneLeapPlusDatasetCached.collate_fn
        )
        
        logger.info(f"✅ 数据集创建成功，样本数: {len(dataset)}")
        
        # 4. 测试数据加载
        logger.info("📥 测试数据加载...")
        batch = next(iter(dataloader))
        
        # 检查数据格式
        required_keys = ['scene_pc', 'hand_model_pose', 'se3', 'positive_prompt']
        for key in required_keys:
            if key not in batch:
                raise ValueError(f"缺少必需的键: {key}")
        
        logger.info(f"场景点云形状: {batch['scene_pc'].shape}")
        logger.info(f"手部姿态形状: {batch['hand_model_pose'].shape}")
        logger.info(f"SE3变换形状: {batch['se3'].shape}")
        logger.info("✅ 数据加载测试通过")
        
        # 5. 测试前向传播
        logger.info("➡️ 测试前向传播...")
        model.eval()
        
        with torch.no_grad():
            # 数据预处理
            from utils.hand_helper import process_hand_pose
            processed_batch = process_hand_pose(batch, rot_type=model.rot_type, mode=model.mode)
            
            # 条件编码
            condition_dict = model.eps_model.condition(processed_batch)
            processed_batch.update(condition_dict)
            
            # 前向传播
            output = model.forward(processed_batch)
            
            logger.info(f"模型输出形状: {output['pred_pose_norm'].shape}")
            logger.info("✅ 前向传播测试通过")
        
        # 6. 测试损失计算
        logger.info("📉 测试损失计算...")
        model.train()
        
        loss_dict = model._compute_loss(batch, mode='train')
        total_loss = sum(v * model.loss_weights.get(k, 1.0) for k, v in loss_dict.items() if torch.is_tensor(v))
        
        logger.info(f"损失项: {list(loss_dict.keys())}")
        logger.info(f"总损失: {total_loss:.6f}")
        logger.info("✅ 损失计算测试通过")
        
        # 7. 测试反向传播
        logger.info("⬅️ 测试反向传播...")
        
        # 清零梯度
        model.zero_grad()
        
        # 反向传播
        total_loss.backward()
        
        # 检查梯度
        has_grad = False
        for name, param in model.named_parameters():
            if param.grad is not None and param.grad.norm() > 0:
                has_grad = True
                break
        
        if has_grad:
            logger.info("✅ 反向传播测试通过")
        else:
            logger.warning("⚠️ 反向传播测试失败：没有检测到梯度")
        
        # 8. 测试简单训练步骤
        logger.info("🏃 测试简单训练步骤...")
        
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
        
        initial_loss = float(total_loss)
        
        for step in range(5):
            optimizer.zero_grad()
            
            loss_dict = model._compute_loss(batch, mode='train')
            total_loss = sum(v * model.loss_weights.get(k, 1.0) for k, v in loss_dict.items() if torch.is_tensor(v))
            
            total_loss.backward()
            optimizer.step()
            
            logger.info(f"Step {step+1}: Loss = {total_loss:.6f}")
        
        final_loss = float(total_loss)
        
        if final_loss < initial_loss:
            logger.info(f"✅ 训练步骤测试通过，损失从 {initial_loss:.6f} 降到 {final_loss:.6f}")
        else:
            logger.warning(f"⚠️ 训练步骤测试警告，损失从 {initial_loss:.6f} 变为 {final_loss:.6f}")
        
        logger.info("\n🎉 快速验证完成！所有基本功能正常。")
        return True
        
    except Exception as e:
        logger.error(f"❌ 快速验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 配置环境
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    # 运行快速验证
    success = quick_validation()
    
    if success:
        logger.info("\n✅ 模型基础功能验证通过，可以进行进一步的训练和优化。")
        logger.info("\n📋 建议的后续步骤：")
        logger.info("1. 运行完整的模型验证: python tests/test_comprehensive_model_validation.py")
        logger.info("2. 进行超参数优化: python tests/test_hyperparameter_optimization.py")
        logger.info("3. 开始正式训练: python train_lightning.py")
    else:
        logger.error("\n❌ 模型基础功能验证失败，需要检查和修复问题。")
    
    return success

if __name__ == "__main__":
    main()
